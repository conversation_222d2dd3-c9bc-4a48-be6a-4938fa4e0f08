import torch
import torch.nn.functional as F

def get_sam_masks_dummy(batch_size, h, w, num_segments=3):
    """
    Generate dummy SAM masks for testing purposes.
    In practice, replace this with actual SAM model inference.

    Args:
        batch_size (int): Batch size
        h (int): Height of feature map
        w (int): Width of feature map
        num_segments (int): Number of different segments

    Returns:
        torch.Tensor: Dummy masks of shape (batch_size, h, w)
    """
    return torch.randint(0, num_segments, (batch_size, h, w))

class SCLModule:
    def __init__(self, sample_pairs=100, temperature=0.1, margin=0.1):
        """
        Structured Contrastive Learning Module
        Args:
            sample_pairs (int): Number of patch pairs to sample for efficiency
            temperature (float): Temperature parameter for similarity scaling
            margin (float): Margin for contrastive loss
        """
        self.sample_pairs = sample_pairs
        self.temperature = temperature
        self.margin = margin

    def compute_scl_loss(self, features, sam_masks):
        """
        Compute SCL loss based on SAM masks
        Args:
            features (torch.Tensor or list): Patch features, shape (batch, h, w, c) or list of features
            sam_masks (torch.Tensor): SAM masks, shape (batch, h, w)
        Returns:
            torch.Tensor: SCL loss
        """
        # Handle different input types
        if isinstance(features, list):
            # Convert list to tensor
            if len(features) == 0:
                return torch.tensor(0.0)
            features = torch.stack(features) if isinstance(features[0], torch.Tensor) else torch.tensor(features)
        elif hasattr(features, 'shape') and not isinstance(features, torch.Tensor):
            # Handle numpy arrays or other array-like objects
            import numpy as np
            if isinstance(features, np.ndarray):
                features = torch.from_numpy(features).float()
            else:
                features = torch.tensor(features).float()

        if isinstance(features, torch.Tensor) and features.dim() == 2:
            # If features is 2D (flattened), we need to reshape it
            # Assume square spatial dimensions for simplicity
            total_patches = features.shape[0]
            feature_dim = features.shape[1]
            spatial_size = int(total_patches ** 0.5)
            if spatial_size * spatial_size == total_patches:
                features = features.view(1, spatial_size, spatial_size, feature_dim)
            else:
                # If not square, create a reasonable batch structure
                batch_size = 1
                h = w = int(total_patches ** 0.5)
                if h * w != total_patches:
                    h, w = total_patches, 1
                features = features.view(batch_size, h, w, feature_dim)

        # Ensure features is 4D: (batch, h, w, c)
        if features.dim() != 4:
            raise ValueError(f"Features must be 4D tensor (batch, h, w, c), got shape: {features.shape}")

        batch, h, w, c = features.shape
        features = features.view(batch, h * w, c)  # Flatten to (batch, num_patches, channels)

        # Handle sam_masks
        if isinstance(sam_masks, list):
            sam_masks = torch.stack(sam_masks) if isinstance(sam_masks[0], torch.Tensor) else torch.tensor(sam_masks)
        elif hasattr(sam_masks, 'shape') and not isinstance(sam_masks, torch.Tensor):
            # Handle numpy arrays or other array-like objects
            import numpy as np
            if isinstance(sam_masks, np.ndarray):
                sam_masks = torch.from_numpy(sam_masks).long()
            else:
                sam_masks = torch.tensor(sam_masks).long()

        # Ensure sam_masks matches features dimensions
        if sam_masks.shape[:2] != (batch, h * w):
            if sam_masks.dim() == 3 and sam_masks.shape == (batch, h, w):
                sam_masks = sam_masks.view(batch, h * w)
            else:
                # Generate dummy masks if dimensions don't match
                sam_masks = torch.randint(0, 3, (batch, h * w), device=features.device)
        loss_pos, loss_neg = torch.tensor(0.0, device=features.device), torch.tensor(0.0, device=features.device)
        count_pos, count_neg = 0, 0

        # Sample patch pairs for efficiency
        max_pairs = min(self.sample_pairs, h * w)
        if max_pairs <= 1:
            # Not enough patches for comparison
            print(f"DEBUG: Not enough patches for comparison: max_pairs={max_pairs}, h*w={h*w}")
            return torch.tensor(0.0, device=features.device)

        indices = torch.randperm(h * w, device=features.device)[:max_pairs]

        for b in range(batch):
            for i_idx, i in enumerate(indices):
                for j in indices[i_idx+1:]:  # More explicit pair generation
                    sim = F.cosine_similarity(features[b, i:i+1], features[b, j:j+1], dim=-1)

                    if sam_masks[b, i] == sam_masks[b, j]:
                        loss_pos += sim.squeeze()
                        count_pos += 1
                    else:
                        loss_neg += sim.squeeze()
                        count_neg += 1

        # Avoid division by zero
        if count_pos == 0 and count_neg == 0:
            return torch.tensor(0.0, device=features.device)

        loss_pos = loss_pos / max(count_pos, 1)
        loss_neg = loss_neg / max(count_neg, 1)
        final_loss = loss_neg - loss_pos

        return final_loss

    def compute_scl_loss_improved(self, features, sam_masks):
        """
        改进的 SCL 损失计算，解决特征相似性问题
        """
        # 处理输入类型（复用原有逻辑）
        if isinstance(features, list):
            if len(features) == 0:
                return torch.tensor(0.0)
            features = torch.stack(features) if isinstance(features[0], torch.Tensor) else torch.tensor(features)
        elif hasattr(features, 'shape') and not isinstance(features, torch.Tensor):
            import numpy as np
            if isinstance(features, np.ndarray):
                features = torch.from_numpy(features).float()
            else:
                features = torch.tensor(features).float()

        if isinstance(features, torch.Tensor) and features.dim() == 2:
            total_patches = features.shape[0]
            feature_dim = features.shape[1]
            spatial_size = int(total_patches ** 0.5)
            if spatial_size * spatial_size == total_patches:
                features = features.view(1, spatial_size, spatial_size, feature_dim)
            else:
                batch_size = 1
                h = w = int(total_patches ** 0.5)
                if h * w != total_patches:
                    h, w = total_patches, 1
                features = features.view(batch_size, h, w, feature_dim)

        if features.dim() != 4:
            raise ValueError(f"Features must be 4D tensor (batch, h, w, c), got shape: {features.shape}")

        batch, h, w, c = features.shape
        features = features.view(batch, h * w, c)

        # 处理掩码
        if isinstance(sam_masks, list):
            sam_masks = torch.stack(sam_masks) if isinstance(sam_masks[0], torch.Tensor) else torch.tensor(sam_masks)
        elif hasattr(sam_masks, 'shape') and not isinstance(sam_masks, torch.Tensor):
            import numpy as np
            if isinstance(sam_masks, np.ndarray):
                sam_masks = torch.from_numpy(sam_masks).long()
            else:
                sam_masks = torch.tensor(sam_masks).long()

        if sam_masks.shape[:2] != (batch, h * w):
            if sam_masks.dim() == 3 and sam_masks.shape == (batch, h, w):
                sam_masks = sam_masks.view(batch, h * w)
            else:
                sam_masks = torch.randint(0, 3, (batch, h * w), device=features.device)

        # 特征归一化以提高对比度
        features = F.normalize(features, p=2, dim=-1)

        # 使用更高效的矩阵运算
        total_loss = torch.tensor(0.0, device=features.device)
        total_pairs = 0

        for b in range(batch):
            # 计算所有特征对的相似性矩阵
            sim_matrix = torch.mm(features[b], features[b].t()) / self.temperature

            # 创建掩码矩阵
            mask_matrix = sam_masks[b].unsqueeze(0) == sam_masks[b].unsqueeze(1)

            # 只考虑上三角矩阵（避免重复计算）
            triu_indices = torch.triu_indices(h * w, h * w, offset=1, device=features.device)

            # 采样策略：如果补丁太多，随机采样
            if len(triu_indices[0]) > self.sample_pairs:
                sample_indices = torch.randperm(len(triu_indices[0]), device=features.device)[:self.sample_pairs]
                triu_indices = (triu_indices[0][sample_indices], triu_indices[1][sample_indices])

            # 获取相似性和掩码
            similarities = sim_matrix[triu_indices]
            mask_pairs = mask_matrix[triu_indices]

            # 计算正负样本损失
            pos_mask = mask_pairs
            neg_mask = ~mask_pairs

            if pos_mask.sum() > 0 and neg_mask.sum() > 0:
                pos_sim = similarities[pos_mask]
                neg_sim = similarities[neg_mask]

                # 使用对比损失：最大化正样本相似性，最小化负样本相似性
                pos_loss = -torch.log(torch.sigmoid(pos_sim - self.margin)).mean()
                neg_loss = -torch.log(torch.sigmoid(self.margin - neg_sim)).mean()

                batch_loss = pos_loss + neg_loss
                total_loss += batch_loss
                total_pairs += 1

        if total_pairs > 0:
            return total_loss / total_pairs
        else:
            return torch.tensor(0.0, device=features.device)

# Example usage
if __name__ == "__main__":
    print("Testing SCL Module...")

    # Create dummy data for testing
    features = torch.randn(2, 14, 14, 768)  # Example ViT features

    # Create dummy SAM masks (simulating segmentation results)
    # In practice, these would come from SAM model predictions
    sam_masks = torch.randint(0, 3, (2, 14, 14))  # 3 different segments

    # Initialize SCL module
    scl = SCLModule(sample_pairs=50)  # Reduced for faster testing
    loss = scl.compute_scl_loss(features, sam_masks)
    print(f"SCL Loss: {loss.item()}")

    print("SCL Module test completed successfully!")

    # Note: To use with actual SAM model, you would need:
    # 1. Install: pip install segment-anything
    # 2. Download SAM checkpoint file
    # 3. Import: from segment_anything import sam_model_registry, SamPredictor
    # 4. Load model: sam = sam_model_registry['vit_b'](checkpoint='path/to/checkpoint')
    # 5. Use SamPredictor for inference