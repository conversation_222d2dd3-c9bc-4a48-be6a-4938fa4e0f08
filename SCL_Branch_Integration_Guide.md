# SCL 分支集成指南

## 🎯 概述

按照用户的想法，我们重新设计了 SCL 集成方式：**SCL 作为独立分支输出结构化监督信号**，而不是隐式地通过损失函数影响特征学习。

## 🏗️ 新架构设计

### 传统方式 vs 新方式

```python
# 传统方式（隐式SCL）：
features = backbone(image)
scl_loss = scl_module(features, masks)  # 通过反向传播隐式改进
total_loss = detection_loss + scl_weight * scl_loss

# 新方式（显式SCL分支）：
main_features = backbone(image)           # 主分支
scl_signal = scl_branch(image)           # SCL分支：显式监督信号
enhanced_features = fusion(main_features, scl_signal)  # 显式融合
```

### 核心组件

1. **SCLBranch**: 独立的结构化信号提取分支
2. **StructureFusionModule**: 主特征与SCL信号的融合模块
3. **SCLSupervisedLoss**: SCL分支的监督损失

## 🔧 使用方法

### 基本用法

```python
from src.softpatch import SoftPatch

# 创建启用SCL分支的SoftPatch
softpatch = SoftPatch(
    device=device,
    input_shape=(3, 224, 224),
    backbone_name="wideresnet50",
    layers_to_extract_from=["layer2", "layer3"],
    
    # 启用SCL分支
    use_scl_branch=True,
    scl_branch_weight=0.2,      # SCL分支损失权重
    scl_fusion_dim=1024,        # 融合后特征维度
    
    # 可以同时启用原有SCL（可选）
    use_scl=False,              # 建议关闭以专注测试新分支
    
    # 其他参数...
    pretrain_embed_dimension=1024,
    target_embed_dimension=1024,
    patchsize=3,
    patchstride=1,
    anomaly_score_num_nn=1,
    featuresampler_percentage=0.1
)

# 训练（会自动包含SCL分支训练）
softpatch.fit(train_loader)

# 推理
scores, masks = softpatch.predict(test_images)
```

### 高级配置

```python
# 自定义SCL分支参数
softpatch = SoftPatch(
    # ... 基本参数 ...
    
    # SCL分支配置
    use_scl_branch=True,
    scl_branch_weight=0.3,      # 增加SCL分支影响
    scl_fusion_dim=2048,        # 更大的融合维度
)
```

## 🔍 工作原理

### 1. SCL分支架构

```python
class SCLBranch(nn.Module):
    def __init__(self, input_channels=3, output_channels=64, spatial_size=14):
        # 轻量级特征提取器
        self.structure_extractor = nn.Sequential(...)
        
        # 结构化注意力
        self.structure_attention = nn.Sequential(...)
        
        # 异常概率预测头
        self.anomaly_head = nn.Sequential(...)
    
    def forward(self, image):
        structure_features = self.structure_extractor(image)
        attention_weights = self.structure_attention(structure_features)
        structure_signal = structure_features * attention_weights
        anomaly_map = self.anomaly_head(structure_signal)
        
        return structure_signal, anomaly_map, attention_weights
```

### 2. 融合策略

```python
class StructureFusionModule(nn.Module):
    def forward(self, main_features, structure_signal, anomaly_map=None):
        # 特征对齐
        main_proj = self.main_proj(main_features)
        structure_proj = self.structure_proj(structure_signal)
        
        # 注意力融合
        attention_weights = self.attention_fusion(combined)
        attended_main = main_proj * attention_weights
        
        # 门控融合
        gate_weights = self.gate(combined)
        gated_structure = structure_proj * gate_weights
        
        # 最终融合
        fused = attended_main + gated_structure
        
        # 异常调制（可选）
        if anomaly_map is not None:
            anomaly_weight = torch.sigmoid(anomaly_map)
            fused = fused * (1 + anomaly_weight)
        
        return fused
```

### 3. 训练流程

```python
def _scl_branch_training(self, training_data):
    # 1. 设置训练模式
    self.scl_branch.train()
    self.fusion_module.train()
    
    # 2. 优化器
    scl_params = list(self.scl_branch.parameters()) + \
                list(self.fusion_module.parameters())
    optimizer = torch.optim.Adam(scl_params, lr=1e-4)
    
    # 3. 训练循环
    for batch in training_data:
        # 前向传播（包含SCL分支融合）
        features = self._embed(images, detach=False)
        
        # 计算SCL监督损失
        scl_loss = self.scl_supervised_loss(
            structure_signal, anomaly_map
        )
        
        # 反向传播
        total_loss = self.scl_branch_weight * scl_loss
        total_loss.backward()
        optimizer.step()
```

## 📊 优势分析

### 相比传统隐式SCL的优势

1. **更直接的监督**
   - 显式的结构化信号
   - 可观察的中间结果
   - 更好的可解释性

2. **更灵活的控制**
   - 可以调整融合策略
   - 可以单独优化SCL分支
   - 支持多种融合方式

3. **更强的表达能力**
   - 异常概率图直接指导
   - 注意力机制增强
   - 多尺度信息融合

### 技术特点

- **独立性**: SCL分支独立于主特征提取
- **可控性**: 通过权重控制SCL影响程度
- **扩展性**: 易于添加新的监督信号
- **效率**: 轻量级SCL分支设计

## 🧪 测试验证

运行测试脚本验证集成效果：

```bash
python test_scl_branch_integration.py
```

测试包括：
1. SCL分支组件测试
2. 集成SoftPatch测试
3. 对比原始方法测试

## 🔧 参数调优

### 关键参数

- `scl_branch_weight`: SCL分支损失权重 (推荐: 0.1-0.3)
- `scl_fusion_dim`: 融合后特征维度 (推荐: 1024-2048)
- SCL分支学习率: 1e-4 (在代码中硬编码)

### 调优建议

1. **权重调整**
   ```python
   # 开始时使用较小权重
   scl_branch_weight=0.1
   
   # 如果SCL效果明显，可以增加
   scl_branch_weight=0.2-0.3
   ```

2. **融合维度**
   ```python
   # 与主特征维度相同
   scl_fusion_dim=target_embed_dimension
   
   # 或者更大以增加表达能力
   scl_fusion_dim=target_embed_dimension * 2
   ```

## 🚀 未来扩展

### 可能的改进方向

1. **多尺度SCL分支**
   ```python
   # 不同尺度的SCL分支
   scl_branch_low = SCLBranch(spatial_size=28)
   scl_branch_high = SCLBranch(spatial_size=14)
   ```

2. **自适应融合权重**
   ```python
   # 学习融合权重而不是固定权重
   adaptive_weight = nn.Parameter(torch.tensor(0.5))
   ```

3. **多模态监督信号**
   ```python
   # 添加其他类型的监督信号
   texture_signal = texture_branch(image)
   shape_signal = shape_branch(image)
   ```

## 📝 注意事项

1. **内存使用**: SCL分支会增加内存使用
2. **训练时间**: 额外的SCL分支训练会增加时间
3. **超参数**: 需要调优SCL分支相关参数
4. **兼容性**: 与原有SCL可以同时使用，但建议分别测试

## 🎯 总结

新的SCL分支集成实现了你的想法：
- ✅ SCL作为独立分支
- ✅ 输出显式监督信号
- ✅ 与主特征显式融合
- ✅ 更好的可控性和可解释性

这种设计提供了更直接、更可控的结构化学习方式！
