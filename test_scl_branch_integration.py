#!/usr/bin/env python3
"""
测试新的 SCL 分支集成
按照用户想法：SCL作为独立分支输出监督信号
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset
import logging
import sys
import os

# 添加src目录到路径
sys.path.append('src')

# 修复导入路径
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.softpatch import SoftPatch
from src.scl_branch import SCLBranch, StructureFusionModule, SCLSupervisedLoss

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_synthetic_data(num_samples=32, image_size=224):
    """创建合成数据用于测试"""
    # 创建正常图像（简单纹理）
    normal_images = []
    for i in range(num_samples):
        # 创建规则纹理
        img = torch.zeros(3, image_size, image_size)
        
        # 添加水平条纹
        for y in range(0, image_size, 20):
            img[:, y:y+10, :] = 0.7
        
        # 添加一些噪声
        noise = torch.randn_like(img) * 0.1
        img = torch.clamp(img + noise, 0, 1)
        
        normal_images.append(img)
    
    return torch.stack(normal_images)

def test_scl_branch_components():
    """测试SCL分支组件"""
    logger.info("=== 测试SCL分支组件 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建测试数据
    batch_size = 4
    test_images = create_synthetic_data(batch_size, 224)
    test_images = test_images.to(device)
    
    logger.info(f"测试图像形状: {test_images.shape}")
    
    # 1. 测试SCL分支
    logger.info("\n1. 测试SCL分支...")
    scl_branch = SCLBranch(input_channels=3, output_channels=64, spatial_size=14).to(device)
    
    with torch.no_grad():
        structure_signal, anomaly_map, attention_weights = scl_branch(test_images)
    
    logger.info(f"结构化信号形状: {structure_signal.shape}")
    logger.info(f"异常概率图形状: {anomaly_map.shape}")
    logger.info(f"注意力权重形状: {attention_weights.shape}")
    
    # 2. 测试融合模块
    logger.info("\n2. 测试融合模块...")
    main_feature_dim = 1024
    structure_dim = 64
    output_dim = 1024
    
    fusion_module = StructureFusionModule(
        main_feature_dim=main_feature_dim,
        structure_dim=structure_dim,
        output_dim=output_dim
    ).to(device)
    
    # 创建模拟的主特征
    num_patches = 14 * 14
    main_features = torch.randn(batch_size * num_patches, main_feature_dim).to(device)
    
    with torch.no_grad():
        fused_features = fusion_module(main_features, structure_signal, anomaly_map)
    
    logger.info(f"主特征形状: {main_features.shape}")
    logger.info(f"融合后特征形状: {fused_features.shape}")
    
    # 3. 测试SCL监督损失
    logger.info("\n3. 测试SCL监督损失...")
    scl_loss_fn = SCLSupervisedLoss()
    
    scl_loss = scl_loss_fn(structure_signal, anomaly_map)
    logger.info(f"SCL监督损失: {scl_loss.item():.6f}")
    
    return True

def test_softpatch_with_scl_branch():
    """测试集成SCL分支的SoftPatch"""
    logger.info("\n=== 测试集成SCL分支的SoftPatch ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建SoftPatch实例（启用SCL分支）
    softpatch = SoftPatch(
        device=device,
        input_shape=(3, 224, 224),
        backbone_name="wideresnet50",
        layers_to_extract_from=["layer2", "layer3"],
        pretrain_embed_dimension=1024,
        target_embed_dimension=1024,
        patchsize=3,
        patchstride=1,
        anomaly_score_num_nn=1,
        featuresampler_percentage=0.1,
        # 启用新的SCL分支
        use_scl_branch=True,
        scl_branch_weight=0.2,
        scl_fusion_dim=1024,
        # 保持原有SCL关闭以专注测试新分支
        use_scl=False
    )
    
    logger.info("SoftPatch实例创建成功")
    logger.info(f"SCL分支启用: {softpatch.use_scl_branch}")
    logger.info(f"SCL分支权重: {softpatch.scl_branch_weight}")
    
    # 创建训练数据
    num_train_samples = 16
    train_images = create_synthetic_data(num_train_samples, 224)
    train_dataset = TensorDataset(train_images)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
    
    logger.info(f"训练数据: {num_train_samples} 样本")
    
    # 测试训练过程
    logger.info("\n开始训练...")
    try:
        softpatch.fit(train_loader)
        logger.info("训练完成!")
    except Exception as e:
        logger.error(f"训练过程中出错: {e}")
        return False
    
    # 测试推理
    logger.info("\n测试推理...")
    test_images = create_synthetic_data(4, 224)
    
    try:
        with torch.no_grad():
            scores, masks = softpatch.predict(test_images.to(device))
        
        logger.info(f"异常分数数量: {len(scores)}")
        logger.info(f"异常掩码数量: {len(masks)}")
        
        if len(scores) > 0:
            logger.info(f"分数范围: {min(scores):.4f} - {max(scores):.4f}")
            logger.info(f"掩码形状: {masks[0].shape if len(masks) > 0 else 'None'}")
        
    except Exception as e:
        logger.error(f"推理过程中出错: {e}")
        return False
    
    return True

def test_scl_branch_vs_original():
    """对比SCL分支和原始方法的效果"""
    logger.info("\n=== 对比SCL分支和原始方法 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    num_samples = 8
    test_images = create_synthetic_data(num_samples, 224)
    
    # 1. 原始SoftPatch（无SCL）
    logger.info("\n1. 测试原始SoftPatch...")
    softpatch_original = SoftPatch(
        device=device,
        input_shape=(3, 224, 224),
        backbone_name="wideresnet50",
        layers_to_extract_from=["layer2", "layer3"],
        pretrain_embed_dimension=1024,
        target_embed_dimension=1024,
        patchsize=3,
        patchstride=1,
        anomaly_score_num_nn=1,
        featuresampler_percentage=0.1,
        use_scl=False,
        use_scl_branch=False
    )
    
    # 2. SCL分支SoftPatch
    logger.info("\n2. 测试SCL分支SoftPatch...")
    softpatch_scl_branch = SoftPatch(
        device=device,
        input_shape=(3, 224, 224),
        backbone_name="wideresnet50",
        layers_to_extract_from=["layer2", "layer3"],
        pretrain_embed_dimension=1024,
        target_embed_dimension=1024,
        patchsize=3,
        patchstride=1,
        anomaly_score_num_nn=1,
        featuresampler_percentage=0.1,
        use_scl=False,
        use_scl_branch=True,
        scl_branch_weight=0.2,
        scl_fusion_dim=1024
    )
    
    # 创建简单的训练数据
    train_dataset = TensorDataset(test_images)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=False)
    
    # 训练两个模型
    logger.info("\n训练原始模型...")
    softpatch_original.fit(train_loader)
    
    logger.info("\n训练SCL分支模型...")
    softpatch_scl_branch.fit(train_loader)
    
    # 比较特征提取
    logger.info("\n比较特征提取...")
    with torch.no_grad():
        # 原始特征
        features_original = softpatch_original._embed(
            test_images[:2].to(device), detach=False
        )
        
        # SCL分支特征
        features_scl_branch = softpatch_scl_branch._embed(
            test_images[:2].to(device), detach=False
        )
    
    logger.info(f"原始特征形状: {features_original.shape}")
    logger.info(f"SCL分支特征形状: {features_scl_branch.shape}")
    
    # 计算特征差异
    if features_original.shape == features_scl_branch.shape:
        feature_diff = torch.norm(features_original - features_scl_branch).item()
        logger.info(f"特征差异 (L2范数): {feature_diff:.6f}")
    else:
        logger.info("特征形状不同，无法直接比较")
    
    return True

def main():
    """主测试函数"""
    logger.info("开始测试新的SCL分支集成...")
    
    try:
        # 测试1: SCL分支组件
        success1 = test_scl_branch_components()
        
        # 测试2: 集成的SoftPatch
        success2 = test_softpatch_with_scl_branch()
        
        # 测试3: 对比测试
        success3 = test_scl_branch_vs_original()
        
        if success1 and success2 and success3:
            logger.info("\n🎉 所有测试通过!")
            logger.info("新的SCL分支集成工作正常")
        else:
            logger.error("\n❌ 部分测试失败")
            
    except Exception as e:
        logger.error(f"\n💥 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
