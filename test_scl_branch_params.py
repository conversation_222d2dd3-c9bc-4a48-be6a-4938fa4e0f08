#!/usr/bin/env python3
"""
测试SCL分支参数传递
"""

import os
import sys
import subprocess

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_scl_branch_params():
    """测试SCL分支参数是否正确传递"""
    print("=== 测试SCL分支参数传递 ===")
    
    # 创建一个简单的测试命令
    test_cmd = [
        "python", "-c", """
import sys
sys.path.append('.')
import argparse

# 复制main.py中的参数解析逻辑
parser = argparse.ArgumentParser()
parser.add_argument("--use_scl", action='store_true')
parser.add_argument("--scl_weight", type=float, default=0.1)
parser.add_argument("--scl_sample_pairs", type=int, default=100)
parser.add_argument("--use_scl_branch", action='store_true')
parser.add_argument("--scl_branch_weight", type=float, default=0.2)
parser.add_argument("--scl_fusion_dim", type=int, default=1024)

# 测试参数解析
test_args = [
    '--use_scl_branch',
    '--scl_branch_weight', '0.3',
    '--scl_fusion_dim', '2048'
]

args = parser.parse_args(test_args)

print(f"✅ SCL分支参数解析成功:")
print(f"  use_scl_branch: {args.use_scl_branch}")
print(f"  scl_branch_weight: {args.scl_branch_weight}")
print(f"  scl_fusion_dim: {args.scl_fusion_dim}")
print(f"  use_scl: {args.use_scl}")
"""
    ]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 参数解析测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ 参数解析测试失败")
            print("错误:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_softpatch_creation():
    """测试SoftPatch创建是否支持新参数"""
    print("\n=== 测试SoftPatch创建 ===")
    
    test_cmd = [
        "python", "-c", """
import sys
sys.path.append('.')
import torch
from src.softpatch import SoftPatch
from src.backbones import load as load_backbone
from src.sampler import ApproximateGreedyCoresetSampler
from src.common import FaissNN

try:
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建SoftPatch实例
    softpatch = SoftPatch(device)
    
    # 加载backbone
    backbone = load_backbone("wideresnet50")
    featuresampler = ApproximateGreedyCoresetSampler(percentage=0.1, device=device)
    nn_method = FaissNN(False, 4)
    
    # 测试load方法是否接受新参数
    softpatch.load(
        backbone=backbone,
        device=device,
        input_shape=(3, 224, 224),
        layers_to_extract_from=["layer2", "layer3"],
        pretrain_embed_dimension=1024,
        target_embed_dimension=1024,
        featuresampler=featuresampler,
        nn_method=nn_method,
        # 测试新的SCL分支参数
        use_scl_branch=True,
        scl_branch_weight=0.3,
        scl_fusion_dim=2048,
        # 旧SCL参数
        use_scl=False
    )
    
    print("✅ SoftPatch创建成功")
    print(f"  SCL分支启用: {softpatch.use_scl_branch}")
    print(f"  SCL分支权重: {softpatch.scl_branch_weight}")
    print(f"  SCL融合维度: {softpatch.scl_fusion_dim}")
    
    # 检查组件是否正确初始化
    if hasattr(softpatch, 'scl_branch') and softpatch.scl_branch is not None:
        print("  ✅ SCL分支组件已初始化")
    else:
        print("  ❌ SCL分支组件未初始化")
        
    if hasattr(softpatch, 'fusion_module') and softpatch.fusion_module is not None:
        print("  ✅ 融合模块已初始化")
    else:
        print("  ❌ 融合模块未初始化")

except Exception as e:
    print(f"❌ SoftPatch创建失败: {e}")
    import traceback
    traceback.print_exc()
"""
    ]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ SoftPatch创建测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ SoftPatch创建测试失败")
            print("错误:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_main_py_integration():
    """测试main.py集成"""
    print("\n=== 测试main.py集成 ===")
    
    # 测试help输出是否包含新参数
    test_cmd = ["python", "main.py", "--help"]
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            help_output = result.stdout
            
            # 检查是否包含新参数
            scl_branch_params = [
                "--use_scl_branch",
                "--scl_branch_weight", 
                "--scl_fusion_dim"
            ]
            
            found_params = []
            for param in scl_branch_params:
                if param in help_output:
                    found_params.append(param)
            
            print(f"✅ main.py help测试通过")
            print(f"  找到SCL分支参数: {found_params}")
            
            if len(found_params) == len(scl_branch_params):
                print("  ✅ 所有SCL分支参数都已添加")
                return True
            else:
                print("  ⚠️ 部分SCL分支参数缺失")
                return False
        else:
            print("❌ main.py help测试失败")
            print("错误:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试SCL分支参数集成...")
    
    results = []
    
    # 测试1: 参数解析
    results.append(test_scl_branch_params())
    
    # 测试2: SoftPatch创建
    results.append(test_softpatch_creation())
    
    # 测试3: main.py集成
    results.append(test_main_py_integration())
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 参数集成测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有参数集成测试通过!")
        print("✅ 新的SCL分支参数已正确集成到系统中")
        print("✅ 可以使用以下命令测试:")
        print("   python main.py --dataset mvtec --data_path ./mvtec_anomaly_detection --use_scl_branch")
    else:
        print("❌ 部分参数集成测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    main()
