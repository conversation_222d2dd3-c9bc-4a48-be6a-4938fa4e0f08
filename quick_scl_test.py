#!/usr/bin/env python3
"""
快速SCL分支测试
"""

import os
import sys
import subprocess
import time

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def quick_test():
    """快速测试SCL分支"""
    print("=== 快速SCL分支测试 ===")
    
    # 检查数据是否存在
    if not os.path.exists("./mvtec_anomaly_detection"):
        print("❌ 未找到MVTec数据集")
        return False
    
    print("✅ 找到MVTec数据集")
    
    # 快速测试命令（使用很小的采样率）
    test_cmd = [
        "python", "main.py",
        "--dataset", "mvtec",
        "--data_path", "./mvtec_anomaly_detection", 
        "--noise", "0.08",
        "--seed", "0",
        "--gpu", "0",
        "--resize", "256",  # 更小的图像尺寸
        "--imagesize", "256",
        "--sampling_ratio", "0.001",  # 非常小的采样率
        "--subdatasets", "pill",
        "--use_scl_branch",  # 使用新的SCL分支
        "--scl_branch_weight", "0.2",
        "--scl_fusion_dim", "1024"
    ]
    
    print("测试命令:", " ".join(test_cmd))
    print("开始测试...")
    
    start_time = time.time()
    
    try:
        # 运行测试，设置较短的超时时间
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=600)  # 10分钟超时
        end_time = time.time()
        
        print(f"测试完成，耗时: {end_time - start_time:.1f}秒")
        
        if result.returncode == 0:
            print("✅ SCL分支测试成功!")
            
            # 查找结果
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if 'image_auroc' in line or 'pixel_auroc' in line or 'Mean' in line:
                    print(f"📊 {line.strip()}")
            
            # 检查是否有SCL相关的日志
            scl_logs = [line for line in output_lines if 'SCL' in line or 'scl' in line]
            if scl_logs:
                print(f"🔍 SCL相关日志 ({len(scl_logs)}条):")
                for log in scl_logs[:5]:  # 显示前5条
                    print(f"   {log.strip()}")
            
            return True
        else:
            print("❌ SCL分支测试失败")
            print("返回码:", result.returncode)
            
            # 显示错误信息
            if result.stderr:
                error_lines = result.stderr.split('\n')
                print("错误信息:")
                for line in error_lines[-10:]:  # 显示最后10行错误
                    if line.strip():
                        print(f"   {line.strip()}")
            
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def compare_with_baseline():
    """与基线对比"""
    print("\n=== 与基线对比 ===")
    
    if not os.path.exists("./mvtec_anomaly_detection"):
        print("❌ 未找到MVTec数据集，跳过对比测试")
        return False
    
    # 基线测试
    baseline_cmd = [
        "python", "main.py",
        "--dataset", "mvtec",
        "--data_path", "./mvtec_anomaly_detection",
        "--noise", "0.08", 
        "--seed", "0",
        "--gpu", "0",
        "--resize", "256",
        "--imagesize", "256",
        "--sampling_ratio", "0.001",
        "--subdatasets", "pill"
        # 不使用任何SCL
    ]
    
    print("🔄 运行基线测试...")
    start_time = time.time()
    
    try:
        baseline_result = subprocess.run(baseline_cmd, capture_output=True, text=True, timeout=600)
        baseline_time = time.time() - start_time
        
        if baseline_result.returncode == 0:
            print(f"✅ 基线测试完成 ({baseline_time:.1f}秒)")
            
            # SCL分支测试
            scl_cmd = baseline_cmd + [
                "--use_scl_branch",
                "--scl_branch_weight", "0.2"
            ]
            
            print("🔄 运行SCL分支测试...")
            start_time = time.time()
            
            scl_result = subprocess.run(scl_cmd, capture_output=True, text=True, timeout=600)
            scl_time = time.time() - start_time
            
            if scl_result.returncode == 0:
                print(f"✅ SCL分支测试完成 ({scl_time:.1f}秒)")
                
                # 对比结果
                print("\n📊 对比结果:")
                print(f"   基线时间: {baseline_time:.1f}秒")
                print(f"   SCL分支时间: {scl_time:.1f}秒")
                print(f"   时间差异: {scl_time - baseline_time:+.1f}秒")
                
                # 尝试提取性能指标
                def extract_metrics(output):
                    lines = output.split('\n')
                    metrics = {}
                    for line in lines:
                        if 'image_auroc' in line and ',' in line:
                            parts = line.split(',')
                            if len(parts) >= 2:
                                try:
                                    metrics['image_auroc'] = float(parts[1])
                                except:
                                    pass
                        elif 'pixel_auroc' in line and ',' in line:
                            parts = line.split(',')
                            if len(parts) >= 2:
                                try:
                                    metrics['pixel_auroc'] = float(parts[1])
                                except:
                                    pass
                    return metrics
                
                baseline_metrics = extract_metrics(baseline_result.stdout)
                scl_metrics = extract_metrics(scl_result.stdout)
                
                if baseline_metrics and scl_metrics:
                    print("\n📈 性能对比:")
                    for metric in ['image_auroc', 'pixel_auroc']:
                        if metric in baseline_metrics and metric in scl_metrics:
                            baseline_val = baseline_metrics[metric]
                            scl_val = scl_metrics[metric]
                            diff = scl_val - baseline_val
                            print(f"   {metric}:")
                            print(f"     基线: {baseline_val:.4f}")
                            print(f"     SCL分支: {scl_val:.4f}")
                            print(f"     差异: {diff:+.4f}")
                
                return True
            else:
                print(f"❌ SCL分支测试失败")
                return False
        else:
            print(f"❌ 基线测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 对比测试超时")
        return False
    except Exception as e:
        print(f"❌ 对比测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速SCL分支测试...")
    
    # 快速测试
    quick_success = quick_test()
    
    if quick_success:
        print("\n🎉 快速测试成功! SCL分支工作正常")
        
        # 如果快速测试成功，可以选择运行对比测试
        print("\n是否运行对比测试? (这会花费更多时间)")
        # 自动运行对比测试
        compare_success = compare_with_baseline()
        
        if compare_success:
            print("\n🏆 对比测试也成功!")
            print("✅ 你的SCL分支设计完全可以替代旧的SCL")
        else:
            print("\n⚠️ 对比测试有问题，但快速测试成功说明SCL分支本身是工作的")
    else:
        print("\n❌ 快速测试失败，需要进一步调试")
    
    return quick_success

if __name__ == "__main__":
    main()
