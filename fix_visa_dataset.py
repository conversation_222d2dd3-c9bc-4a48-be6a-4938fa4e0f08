#!/usr/bin/env python3
"""
修复VISA数据集中的损坏图像
"""

import os
import sys
from PIL import Image
import shutil
from pathlib import Path

def check_image_file(image_path):
    """检查图像文件是否可以正常打开"""
    try:
        with Image.open(image_path) as img:
            # 尝试加载图像数据
            img.verify()
        return True, None
    except Exception as e:
        return False, str(e)

def find_corrupted_images(dataset_path):
    """查找数据集中所有损坏的图像"""
    print(f"🔍 检查数据集: {dataset_path}")
    
    corrupted_files = []
    total_files = 0
    
    # 支持的图像格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    for root, dirs, files in os.walk(dataset_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            if file_ext in image_extensions:
                total_files += 1
                is_valid, error = check_image_file(file_path)
                
                if not is_valid:
                    corrupted_files.append({
                        'path': file_path,
                        'error': error,
                        'size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    })
                    print(f"❌ 损坏文件: {file_path}")
                    print(f"   错误: {error}")
                    print(f"   大小: {os.path.getsize(file_path)} bytes")
                
                if total_files % 100 == 0:
                    print(f"   已检查 {total_files} 个文件...")
    
    print(f"\n📊 检查完成:")
    print(f"   总文件数: {total_files}")
    print(f"   损坏文件数: {len(corrupted_files)}")
    
    return corrupted_files

def fix_specific_file(file_path):
    """尝试修复特定的损坏文件"""
    print(f"\n🔧 尝试修复: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print("❌ 文件不存在")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} bytes")
    
    if file_size == 0:
        print("❌ 文件为空")
        return False
    
    # 尝试不同的修复方法
    backup_path = file_path + ".backup"
    
    try:
        # 1. 备份原文件
        shutil.copy2(file_path, backup_path)
        print("✅ 已创建备份")
        
        # 2. 尝试用PIL重新保存
        try:
            with Image.open(file_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 重新保存
                temp_path = file_path + ".temp"
                img.save(temp_path, 'JPEG', quality=95)
                
                # 验证修复后的文件
                is_valid, error = check_image_file(temp_path)
                if is_valid:
                    shutil.move(temp_path, file_path)
                    print("✅ 修复成功")
                    return True
                else:
                    os.remove(temp_path)
                    print(f"❌ 修复失败: {error}")
        except Exception as e:
            print(f"❌ PIL修复失败: {e}")
        
        # 3. 如果修复失败，删除损坏文件
        print("⚠️ 无法修复，建议删除此文件")
        return False
        
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False

def remove_corrupted_files(corrupted_files, confirm=True):
    """删除损坏的文件"""
    if not corrupted_files:
        print("✅ 没有损坏的文件需要删除")
        return
    
    print(f"\n🗑️ 准备删除 {len(corrupted_files)} 个损坏文件:")
    
    for file_info in corrupted_files:
        print(f"   {file_info['path']}")
    
    if confirm:
        response = input("\n确认删除这些文件吗? (y/N): ")
        if response.lower() != 'y':
            print("❌ 取消删除操作")
            return
    
    deleted_count = 0
    for file_info in corrupted_files:
        try:
            file_path = file_info['path']
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ 已删除: {file_path}")
                deleted_count += 1
        except Exception as e:
            print(f"❌ 删除失败 {file_path}: {e}")
    
    print(f"\n📊 删除完成: {deleted_count}/{len(corrupted_files)} 个文件")

def fix_visa_dataset():
    """修复VISA数据集"""
    print("=== 修复VISA数据集 ===")
    
    # 检查VISA数据集路径
    visa_paths = [
        "./visa",
        "./VisA",
        "./visa_dataset",
        "F:\\wxx\\AnomalyDetection-SoftPatch-main - 副本 (2)\\visa"
    ]
    
    visa_path = None
    for path in visa_paths:
        if os.path.exists(path):
            visa_path = path
            break
    
    if not visa_path:
        print("❌ 未找到VISA数据集")
        print("请确保VISA数据集在以下路径之一:")
        for path in visa_paths:
            print(f"   {path}")
        return False
    
    print(f"✅ 找到VISA数据集: {visa_path}")
    
    # 首先检查特定的问题文件
    problem_file = os.path.join(visa_path, "cashew", "train", "good", "240.JPG")
    if os.path.exists(problem_file):
        print(f"\n🎯 检查问题文件: {problem_file}")
        is_valid, error = check_image_file(problem_file)
        if not is_valid:
            print(f"❌ 确认文件损坏: {error}")
            
            # 尝试修复
            if not fix_specific_file(problem_file):
                print("⚠️ 修复失败，删除此文件")
                try:
                    os.remove(problem_file)
                    print("✅ 已删除损坏文件")
                except Exception as e:
                    print(f"❌ 删除失败: {e}")
        else:
            print("✅ 文件正常")
    
    # 全面检查数据集
    print(f"\n🔍 全面检查VISA数据集...")
    corrupted_files = find_corrupted_images(visa_path)
    
    if corrupted_files:
        print(f"\n发现 {len(corrupted_files)} 个损坏文件")
        
        # 尝试修复小文件
        small_files = [f for f in corrupted_files if f['size'] < 1000]  # 小于1KB的文件
        if small_files:
            print(f"\n删除 {len(small_files)} 个小文件:")
            for file_info in small_files:
                try:
                    os.remove(file_info['path'])
                    print(f"✅ 已删除: {file_info['path']}")
                except Exception as e:
                    print(f"❌ 删除失败: {e}")
        
        # 处理其他损坏文件
        remaining_files = [f for f in corrupted_files if f['size'] >= 1000]
        if remaining_files:
            remove_corrupted_files(remaining_files, confirm=False)
    
    print("\n✅ VISA数据集修复完成")
    return True

def test_dataset_loading():
    """测试数据集加载"""
    print("\n=== 测试数据集加载 ===")
    
    try:
        # 尝试导入数据集模块
        sys.path.append('src')
        from src.datasets.visa import VisADataset
        
        # 创建数据集实例
        dataset = VisADataset(
            source="./visa",
            classname="cashew",
            resize=256,
            imagesize=224,
            split="train",
            train_val_split=1.0
        )
        
        print(f"✅ 数据集创建成功")
        print(f"   数据集大小: {len(dataset)}")
        
        # 尝试加载第一个样本
        if len(dataset) > 0:
            sample = dataset[0]
            print(f"   样本形状: {sample['image'].shape}")
            print("✅ 数据集加载测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集加载测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 VISA数据集修复工具")
    
    # 修复数据集
    fix_success = fix_visa_dataset()
    
    if fix_success:
        # 测试数据集加载
        test_success = test_dataset_loading()
        
        if test_success:
            print("\n🎉 修复完成! 现在可以正常使用VISA数据集了")
            print("\n💡 建议的测试命令:")
            print("python main.py --dataset visa --data_path ./visa --subdatasets cashew --use_scl_branch")
        else:
            print("\n⚠️ 修复完成但数据集加载仍有问题，可能需要重新下载数据集")
    else:
        print("\n❌ 修复失败")
    
    return fix_success

if __name__ == "__main__":
    main()
