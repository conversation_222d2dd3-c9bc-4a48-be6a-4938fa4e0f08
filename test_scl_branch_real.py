#!/usr/bin/env python3
"""
在真实数据上测试SCL分支
"""

import os
import sys
import subprocess
import time

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_scl_branch_vs_baseline():
    """对比SCL分支和基线的效果"""
    print("=== 对比SCL分支和基线效果 ===")
    
    # 基本参数
    base_cmd = [
        "python", "main.py",
        "--dataset", "mvtec",
        "--data_path", "./mvtec_anomaly_detection",
        "--noise", "0.08",
        "--seed", "0",
        "--gpu", "0",
        "--resize", "512",
        "--imagesize", "512",
        "--sampling_ratio", "0.01",
        "--subdatasets", "pill"
    ]
    
    results = {}
    
    # 测试1: 基线（无SCL）
    print("\n🔄 测试1: 基线（无SCL）")
    baseline_cmd = base_cmd.copy()
    
    print("命令:", " ".join(baseline_cmd))
    start_time = time.time()
    
    try:
        result = subprocess.run(baseline_cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ 基线测试完成 ({end_time - start_time:.1f}秒)")
            results['baseline'] = {
                'success': True,
                'time': end_time - start_time,
                'output': result.stdout
            }
        else:
            print(f"❌ 基线测试失败")
            print("错误输出:", result.stderr[-500:])  # 显示最后500字符
            results['baseline'] = {
                'success': False,
                'error': result.stderr
            }
    except subprocess.TimeoutExpired:
        print("❌ 基线测试超时")
        results['baseline'] = {'success': False, 'error': 'Timeout'}
    except Exception as e:
        print(f"❌ 基线测试异常: {e}")
        results['baseline'] = {'success': False, 'error': str(e)}
    
    # 测试2: 旧SCL（有问题的版本）
    print("\n🔄 测试2: 旧SCL（预期失败）")
    old_scl_cmd = base_cmd + ["--use_scl"]
    
    print("命令:", " ".join(old_scl_cmd))
    start_time = time.time()
    
    try:
        result = subprocess.run(old_scl_cmd, capture_output=True, text=True, timeout=1800)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"⚠️ 旧SCL测试完成 ({end_time - start_time:.1f}秒) - 但可能有警告")
            results['old_scl'] = {
                'success': True,
                'time': end_time - start_time,
                'output': result.stdout,
                'warnings': result.stderr
            }
        else:
            print(f"❌ 旧SCL测试失败")
            print("错误输出:", result.stderr[-500:])
            results['old_scl'] = {
                'success': False,
                'error': result.stderr
            }
    except subprocess.TimeoutExpired:
        print("❌ 旧SCL测试超时")
        results['old_scl'] = {'success': False, 'error': 'Timeout'}
    except Exception as e:
        print(f"❌ 旧SCL测试异常: {e}")
        results['old_scl'] = {'success': False, 'error': str(e)}
    
    # 测试3: 新SCL分支
    print("\n🔄 测试3: 新SCL分支（你的设计）")
    new_scl_cmd = base_cmd + [
        "--use_scl_branch",
        "--scl_branch_weight", "0.2",
        "--scl_fusion_dim", "1024"
    ]
    
    print("命令:", " ".join(new_scl_cmd))
    start_time = time.time()
    
    try:
        result = subprocess.run(new_scl_cmd, capture_output=True, text=True, timeout=1800)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ 新SCL分支测试完成 ({end_time - start_time:.1f}秒)")
            results['new_scl_branch'] = {
                'success': True,
                'time': end_time - start_time,
                'output': result.stdout
            }
        else:
            print(f"❌ 新SCL分支测试失败")
            print("错误输出:", result.stderr[-500:])
            results['new_scl_branch'] = {
                'success': False,
                'error': result.stderr
            }
    except subprocess.TimeoutExpired:
        print("❌ 新SCL分支测试超时")
        results['new_scl_branch'] = {'success': False, 'error': 'Timeout'}
    except Exception as e:
        print(f"❌ 新SCL分支测试异常: {e}")
        results['new_scl_branch'] = {'success': False, 'error': str(e)}
    
    return results

def parse_results(results):
    """解析测试结果"""
    print("\n=== 结果分析 ===")
    
    for test_name, result in results.items():
        print(f"\n📊 {test_name.upper()}:")
        
        if result['success']:
            print(f"  ✅ 状态: 成功")
            print(f"  ⏱️ 时间: {result['time']:.1f}秒")
            
            # 尝试提取性能指标
            output = result['output']
            if 'image_auroc' in output:
                lines = output.split('\n')
                for line in lines:
                    if 'image_auroc' in line or 'pixel_auroc' in line:
                        print(f"  📈 {line.strip()}")
            
            # 检查警告
            if 'warnings' in result and result['warnings']:
                warning_lines = [line for line in result['warnings'].split('\n') 
                               if 'WARNING' in line or 'ERROR' in line]
                if warning_lines:
                    print(f"  ⚠️ 警告数量: {len(warning_lines)}")
                    for warning in warning_lines[:3]:  # 显示前3个警告
                        print(f"     {warning.strip()}")
        else:
            print(f"  ❌ 状态: 失败")
            print(f"  🔍 错误: {result['error'][:200]}...")

def create_comparison_summary(results):
    """创建对比总结"""
    print("\n=== 对比总结 ===")
    
    success_count = sum(1 for r in results.values() if r['success'])
    total_count = len(results)
    
    print(f"📊 成功率: {success_count}/{total_count}")
    
    if results.get('baseline', {}).get('success') and results.get('new_scl_branch', {}).get('success'):
        baseline_time = results['baseline']['time']
        scl_time = results['new_scl_branch']['time']
        time_diff = scl_time - baseline_time
        
        print(f"⏱️ 时间对比:")
        print(f"  基线: {baseline_time:.1f}秒")
        print(f"  SCL分支: {scl_time:.1f}秒")
        print(f"  差异: {time_diff:+.1f}秒")
    
    print(f"\n🎯 建议:")
    if results.get('old_scl', {}).get('success') == False:
        print("  ✅ 旧SCL确实有问题，验证了我们的分析")
    
    if results.get('new_scl_branch', {}).get('success'):
        print("  ✅ 新SCL分支工作正常，可以替代旧SCL")
    else:
        print("  ⚠️ 新SCL分支需要进一步调试")

def main():
    """主函数"""
    print("🚀 在真实数据上测试SCL分支...")
    
    # 检查数据路径
    if not os.path.exists("./mvtec_anomaly_detection"):
        print("❌ 未找到MVTec数据集，请确保数据路径正确")
        print("   当前工作目录:", os.getcwd())
        print("   请确保 ./mvtec_anomaly_detection 存在")
        return False
    
    print("✅ 找到MVTec数据集")
    
    # 运行对比测试
    results = test_scl_branch_vs_baseline()
    
    # 分析结果
    parse_results(results)
    
    # 创建总结
    create_comparison_summary(results)
    
    return True

if __name__ == "__main__":
    main()
