#!/usr/bin/env python3
"""
简化的SCL分支测试
"""

import torch
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("Python路径:", sys.path[:3])
print("当前目录:", current_dir)

def test_scl_branch_components():
    """测试SCL分支组件"""
    print("=== 测试SCL分支组件 ===")
    
    try:
        # 导入SCL分支组件
        from src.scl_branch import SCLBranch, StructureFusionModule, SCLSupervisedLoss
        print("✅ SCL分支组件导入成功")
    except Exception as e:
        print(f"❌ SCL分支组件导入失败: {e}")
        return False
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建测试数据
    batch_size = 2
    test_images = torch.randn(batch_size, 3, 224, 224).to(device)
    print(f"测试图像形状: {test_images.shape}")
    
    try:
        # 1. 测试SCL分支
        print("\n1. 测试SCL分支...")
        scl_branch = SCLBranch(input_channels=3, output_channels=64, spatial_size=14).to(device)
        
        with torch.no_grad():
            structure_signal, anomaly_map, attention_weights = scl_branch(test_images)
        
        print(f"✅ 结构化信号形状: {structure_signal.shape}")
        print(f"✅ 异常概率图形状: {anomaly_map.shape}")
        print(f"✅ 注意力权重形状: {attention_weights.shape}")
        
        # 2. 测试融合模块
        print("\n2. 测试融合模块...")
        main_feature_dim = 1024
        structure_dim = 64
        output_dim = 1024
        
        fusion_module = StructureFusionModule(
            main_feature_dim=main_feature_dim,
            structure_dim=structure_dim,
            output_dim=output_dim
        ).to(device)
        
        # 创建模拟的主特征
        num_patches = 14 * 14
        main_features = torch.randn(batch_size * num_patches, main_feature_dim).to(device)
        
        with torch.no_grad():
            fused_features = fusion_module(main_features, structure_signal, anomaly_map)
        
        print(f"✅ 主特征形状: {main_features.shape}")
        print(f"✅ 融合后特征形状: {fused_features.shape}")
        
        # 3. 测试SCL监督损失
        print("\n3. 测试SCL监督损失...")
        scl_loss_fn = SCLSupervisedLoss()
        
        scl_loss = scl_loss_fn(structure_signal, anomaly_map)
        print(f"✅ SCL监督损失: {scl_loss.item():.6f}")
        
        print("\n🎉 SCL分支组件测试全部通过!")
        return True
        
    except Exception as e:
        print(f"❌ SCL分支组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_softpatch_import():
    """测试SoftPatch导入"""
    print("\n=== 测试SoftPatch导入 ===")
    
    try:
        from src.softpatch import SoftPatch
        print("✅ SoftPatch导入成功")
        
        # 检查新的SCL分支参数
        import inspect
        sig = inspect.signature(SoftPatch.__init__)
        params = list(sig.parameters.keys())
        
        scl_branch_params = [p for p in params if 'scl_branch' in p]
        print(f"✅ SCL分支参数: {scl_branch_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ SoftPatch导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scl_branch_creation():
    """测试创建带SCL分支的SoftPatch"""
    print("\n=== 测试创建带SCL分支的SoftPatch ===")
    
    try:
        from src.softpatch import SoftPatch
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建SoftPatch实例
        from src.backbones import load as load_backbone
        from src.sampler import ApproximateGreedyCoresetSampler
        from src.common import FaissNN

        softpatch = SoftPatch(device=device)

        # 加载backbone
        backbone = load_backbone("wideresnet50")

        # 创建featuresampler
        featuresampler = ApproximateGreedyCoresetSampler(percentage=0.1, device=device)

        # 使用load方法配置（启用SCL分支）
        softpatch.load(
            backbone=backbone,
            device=device,
            input_shape=(3, 224, 224),
            layers_to_extract_from=["layer2", "layer3"],
            pretrain_embed_dimension=1024,
            target_embed_dimension=1024,
            patchsize=3,
            patchstride=1,
            anomaly_score_num_nn=1,
            featuresampler=featuresampler,
            # 启用新的SCL分支
            use_scl_branch=True,
            scl_branch_weight=0.2,
            scl_fusion_dim=1024,
            # 保持原有SCL关闭
            use_scl=False
        )
        
        print("✅ SoftPatch实例创建成功")
        print(f"✅ SCL分支启用: {softpatch.use_scl_branch}")
        print(f"✅ SCL分支权重: {softpatch.scl_branch_weight}")
        print(f"✅ SCL融合维度: {softpatch.scl_fusion_dim}")
        
        # 检查SCL分支组件是否正确初始化
        if hasattr(softpatch, 'scl_branch') and softpatch.scl_branch is not None:
            print("✅ SCL分支组件已初始化")
        else:
            print("❌ SCL分支组件未初始化")
            return False
            
        if hasattr(softpatch, 'fusion_module') and softpatch.fusion_module is not None:
            print("✅ 融合模块已初始化")
        else:
            print("❌ 融合模块未初始化")
            return False
        
        print("\n🎉 SCL分支SoftPatch创建测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ SCL分支SoftPatch创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_extraction():
    """测试特征提取"""
    print("\n=== 测试特征提取 ===")
    
    try:
        from src.softpatch import SoftPatch
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 导入必要的组件
        from src.backbones import load as load_backbone
        from src.sampler import ApproximateGreedyCoresetSampler
        from src.common import FaissNN

        backbone = load_backbone("wideresnet50")
        featuresampler = ApproximateGreedyCoresetSampler(percentage=0.1, device=device)

        # 创建两个SoftPatch实例进行对比
        print("创建原始SoftPatch...")
        softpatch_original = SoftPatch(device=device)
        softpatch_original.load(
            backbone=backbone,
            device=device,
            input_shape=(3, 224, 224),
            layers_to_extract_from=["layer2", "layer3"],
            pretrain_embed_dimension=1024,
            target_embed_dimension=1024,
            patchsize=3,
            patchstride=1,
            anomaly_score_num_nn=1,
            featuresampler=featuresampler,
            use_scl=False,
            use_scl_branch=False
        )

        print("创建SCL分支SoftPatch...")
        softpatch_scl = SoftPatch(device=device)
        softpatch_scl.load(
            backbone=backbone,
            device=device,
            input_shape=(3, 224, 224),
            layers_to_extract_from=["layer2", "layer3"],
            pretrain_embed_dimension=1024,
            target_embed_dimension=1024,
            patchsize=3,
            patchstride=1,
            anomaly_score_num_nn=1,
            featuresampler=featuresampler,
            use_scl=False,
            use_scl_branch=True,
            scl_branch_weight=0.2,
            scl_fusion_dim=1024
        )
        
        # 创建测试图像
        test_images = torch.randn(2, 3, 224, 224).to(device)
        print(f"测试图像形状: {test_images.shape}")
        
        # 检查可用的层
        print("\n检查backbone可用层...")
        feature_aggregator = softpatch_original.forward_modules["feature_aggregator"]
        with torch.no_grad():
            all_features = feature_aggregator(test_images)
        print(f"可用层: {list(all_features.keys())}")

        # 测试特征提取
        print("\n提取原始特征...")
        try:
            with torch.no_grad():
                features_original = softpatch_original._embed(test_images, detach=True)
            print(f"✅ 原始特征形状: {np.array(features_original).shape}")
        except Exception as e:
            print(f"⚠️ 原始特征提取失败: {e}")
            # 尝试使用可用的层
            available_layers = list(all_features.keys())[:2]  # 取前两层
            print(f"尝试使用层: {available_layers}")
            softpatch_original.layers_to_extract_from = available_layers
            with torch.no_grad():
                features_original = softpatch_original._embed(test_images, detach=True)
            print(f"✅ 原始特征形状: {np.array(features_original).shape}")

        print("\n提取SCL分支特征...")
        try:
            with torch.no_grad():
                features_scl = softpatch_scl._embed(test_images, detach=True)
            print(f"✅ SCL分支特征形状: {np.array(features_scl).shape}")
        except Exception as e:
            print(f"⚠️ SCL分支特征提取失败: {e}")
            # 使用相同的可用层
            softpatch_scl.layers_to_extract_from = softpatch_original.layers_to_extract_from
            with torch.no_grad():
                features_scl = softpatch_scl._embed(test_images, detach=True)
            print(f"✅ SCL分支特征形状: {np.array(features_scl).shape}")
        
        print("\n🎉 特征提取测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 特征提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新的SCL分支集成...")
    
    results = []
    
    # 测试1: SCL分支组件
    results.append(test_scl_branch_components())
    
    # 测试2: SoftPatch导入
    results.append(test_softpatch_import())
    
    # 测试3: SCL分支SoftPatch创建
    results.append(test_scl_branch_creation())
    
    # 测试4: 特征提取
    results.append(test_feature_extraction())
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! SCL分支集成工作正常")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    main()
