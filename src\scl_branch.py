#!/usr/bin/env python3
"""
SCL Branch Module - 独立的结构化监督分支
按照用户想法重新设计：SCL作为独立分支输出监督信号
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class SCLBranch(nn.Module):
    """
    独立的 SCL 分支，输出结构化监督信号
    """
    def __init__(self, input_channels=3, output_channels=64, spatial_size=14):
        super(SCLBranch, self).__init__()
        
        self.spatial_size = spatial_size
        self.output_channels = output_channels
        
        # 轻量级的特征提取器（专门用于结构化信息）
        self.structure_extractor = nn.Sequential(
            # 第一层：捕获低级结构
            nn.Conv2d(input_channels, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 112x112
            
            # 第二层：中级结构
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.<PERSON>L<PERSON>(inplace=True),
            nn.MaxPool2d(2, 2),  # 56x56
            
            # 第三层：高级结构
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),  # 28x28
            
            # 第四层：结构化表示
            nn.Conv2d(128, output_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(output_channels),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d(spatial_size)  # 调整到目标尺寸
        )
        
        # 结构化注意力模块
        self.structure_attention = nn.Sequential(
            nn.Conv2d(output_channels, output_channels // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(output_channels // 4, output_channels, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 异常概率预测头
        self.anomaly_head = nn.Sequential(
            nn.Conv2d(output_channels, 32, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
    def forward(self, image):
        """
        前向传播，输出结构化监督信号
        
        Args:
            image: 输入图像 [B, 3, H, W]
            
        Returns:
            structure_signal: 结构化信号 [B, output_channels, spatial_size, spatial_size]
            anomaly_map: 异常概率图 [B, 1, spatial_size, spatial_size]
            attention_weights: 注意力权重 [B, output_channels, spatial_size, spatial_size]
        """
        # 提取结构化特征
        structure_features = self.structure_extractor(image)
        
        # 计算结构化注意力
        attention_weights = self.structure_attention(structure_features)
        
        # 应用注意力增强结构化信号
        structure_signal = structure_features * attention_weights
        
        # 预测异常概率图
        anomaly_map = self.anomaly_head(structure_signal)
        
        return structure_signal, anomaly_map, attention_weights

class StructureFusionModule(nn.Module):
    """
    结构化融合模块：将主特征和SCL信号融合
    """
    def __init__(self, main_feature_dim, structure_dim, output_dim):
        super(StructureFusionModule, self).__init__()
        
        self.main_feature_dim = main_feature_dim
        self.structure_dim = structure_dim
        self.output_dim = output_dim
        
        # 特征对齐层
        self.main_proj = nn.Linear(main_feature_dim, output_dim)
        self.structure_proj = nn.Linear(structure_dim, output_dim)
        
        # 融合策略1：注意力融合
        self.attention_fusion = nn.Sequential(
            nn.Linear(output_dim * 2, output_dim),
            nn.ReLU(inplace=True),
            nn.Linear(output_dim, output_dim),
            nn.Sigmoid()
        )
        
        # 融合策略2：门控融合
        self.gate = nn.Sequential(
            nn.Linear(output_dim * 2, output_dim),
            nn.Sigmoid()
        )
        
        # 最终输出层
        self.output_layer = nn.Sequential(
            nn.Linear(output_dim, output_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(output_dim, output_dim)
        )
        
    def forward(self, main_features, structure_signal, anomaly_map=None):
        """
        融合主特征和结构化信号

        Args:
            main_features: 主分支特征 [B*H*W, main_feature_dim]
            structure_signal: SCL结构化信号 [B, structure_dim, H, W]
            anomaly_map: 异常概率图 [B, 1, H, W] (可选)

        Returns:
            fused_features: 融合后的特征 [B*H*W, output_dim]
        """
        B, structure_dim, H, W = structure_signal.shape

        # 计算主特征的空间尺寸
        main_total_patches = main_features.shape[0]
        main_patches_per_batch = main_total_patches // B

        # 检查尺寸匹配
        structure_patches_per_batch = H * W

        if main_patches_per_batch != structure_patches_per_batch:
            # 需要调整结构化信号的尺寸以匹配主特征
            # 使用插值来调整尺寸
            import torch.nn.functional as F

            # 计算目标尺寸
            target_spatial_size = int(main_patches_per_batch ** 0.5)
            if target_spatial_size * target_spatial_size != main_patches_per_batch:
                # 如果不是完全平方数，使用最接近的平方数
                target_spatial_size = int(main_patches_per_batch ** 0.5)
                target_H = target_W = target_spatial_size
                if target_H * target_W < main_patches_per_batch:
                    target_H += 1
            else:
                target_H = target_W = target_spatial_size

            # 调整结构化信号尺寸
            structure_signal_resized = F.interpolate(
                structure_signal,
                size=(target_H, target_W),
                mode='bilinear',
                align_corners=False
            )

            # 如果插值后的尺寸仍然不匹配，进行裁剪或填充
            resized_patches = target_H * target_W
            if resized_patches > main_patches_per_batch:
                # 裁剪多余的部分
                structure_flat = structure_signal_resized.permute(0, 2, 3, 1).reshape(-1, structure_dim)
                structure_flat = structure_flat[:main_total_patches]
            elif resized_patches < main_patches_per_batch:
                # 填充不足的部分
                structure_flat = structure_signal_resized.permute(0, 2, 3, 1).reshape(-1, structure_dim)
                padding_size = main_total_patches - structure_flat.shape[0]
                padding = structure_flat[-1:].repeat(padding_size, 1)
                structure_flat = torch.cat([structure_flat, padding], dim=0)
            else:
                structure_flat = structure_signal_resized.permute(0, 2, 3, 1).reshape(-1, structure_dim)
        else:
            # 尺寸匹配，直接重塑
            structure_flat = structure_signal.permute(0, 2, 3, 1).reshape(-1, structure_dim)

        # 特征投影到相同维度
        main_proj = self.main_proj(main_features)
        structure_proj = self.structure_proj(structure_flat)
        
        # 拼接特征
        combined = torch.cat([main_proj, structure_proj], dim=-1)
        
        # 注意力融合
        attention_weights = self.attention_fusion(combined)
        attended_main = main_proj * attention_weights
        
        # 门控融合
        gate_weights = self.gate(combined)
        gated_structure = structure_proj * gate_weights
        
        # 最终融合
        fused = attended_main + gated_structure
        
        # 如果有异常图，进一步调制
        if anomaly_map is not None:
            # 处理异常图的尺寸匹配问题
            anomaly_B, _, anomaly_H, anomaly_W = anomaly_map.shape
            anomaly_patches = anomaly_H * anomaly_W

            if anomaly_patches != main_patches_per_batch:
                # 调整异常图尺寸
                import torch.nn.functional as F
                target_spatial_size = int(main_patches_per_batch ** 0.5)
                if target_spatial_size * target_spatial_size != main_patches_per_batch:
                    target_spatial_size = int(main_patches_per_batch ** 0.5)
                    target_H = target_W = target_spatial_size
                    if target_H * target_W < main_patches_per_batch:
                        target_H += 1
                else:
                    target_H = target_W = target_spatial_size

                anomaly_map_resized = F.interpolate(
                    anomaly_map,
                    size=(target_H, target_W),
                    mode='bilinear',
                    align_corners=False
                )

                # 处理尺寸不匹配
                resized_patches = target_H * target_W
                if resized_patches > main_patches_per_batch:
                    anomaly_flat = anomaly_map_resized.permute(0, 2, 3, 1).reshape(-1, 1)
                    anomaly_flat = anomaly_flat[:main_total_patches]
                elif resized_patches < main_patches_per_batch:
                    anomaly_flat = anomaly_map_resized.permute(0, 2, 3, 1).reshape(-1, 1)
                    padding_size = main_total_patches - anomaly_flat.shape[0]
                    padding = anomaly_flat[-1:].repeat(padding_size, 1)
                    anomaly_flat = torch.cat([anomaly_flat, padding], dim=0)
                else:
                    anomaly_flat = anomaly_map_resized.permute(0, 2, 3, 1).reshape(-1, 1)
            else:
                anomaly_flat = anomaly_map.permute(0, 2, 3, 1).reshape(-1, 1)

            anomaly_weight = torch.sigmoid(anomaly_flat)
            fused = fused * (1 + anomaly_weight)  # 异常区域特征增强
        
        # 输出层
        output = self.output_layer(fused)
        
        return output

class SCLSupervisedLoss(nn.Module):
    """
    SCL分支的监督损失
    """
    def __init__(self, temperature=0.1, margin=0.1):
        super(SCLSupervisedLoss, self).__init__()
        self.temperature = temperature
        self.margin = margin
        
    def forward(self, structure_signal, anomaly_map, pseudo_masks=None):
        """
        计算SCL分支的监督损失
        
        Args:
            structure_signal: 结构化信号 [B, C, H, W]
            anomaly_map: 异常概率图 [B, 1, H, W]
            pseudo_masks: 伪标签掩码 [B, H, W] (可选)
        """
        B, C, H, W = structure_signal.shape
        
        # 生成伪标签（如果没有提供）
        if pseudo_masks is None:
            pseudo_masks = self.generate_pseudo_masks(anomaly_map)
        
        # 重塑为补丁格式
        structure_flat = structure_signal.view(B, C, -1).permute(0, 2, 1)  # [B, H*W, C]
        masks_flat = pseudo_masks.view(B, -1)  # [B, H*W]
        
        total_loss = 0.0
        valid_batches = 0
        
        for b in range(B):
            # 计算相似度矩阵
            features = F.normalize(structure_flat[b], p=2, dim=-1)
            sim_matrix = torch.mm(features, features.t()) / self.temperature
            
            # 创建正负样本掩码
            masks_b = masks_flat[b]
            pos_mask = (masks_b.unsqueeze(0) == masks_b.unsqueeze(1)).float()
            neg_mask = 1 - pos_mask
            
            # 移除对角线
            eye_mask = torch.eye(H*W, device=structure_signal.device)
            pos_mask = pos_mask * (1 - eye_mask)
            neg_mask = neg_mask * (1 - eye_mask)
            
            # 计算对比损失
            if pos_mask.sum() > 0 and neg_mask.sum() > 0:
                pos_sim = (sim_matrix * pos_mask).sum() / pos_mask.sum()
                neg_sim = (sim_matrix * neg_mask).sum() / neg_mask.sum()
                
                contrastive_loss = F.relu(neg_sim - pos_sim + self.margin)
                total_loss += contrastive_loss
                valid_batches += 1
        
        return total_loss / max(valid_batches, 1)
    
    def generate_pseudo_masks(self, anomaly_map):
        """
        基于异常概率图生成伪标签
        """
        B, _, H, W = anomaly_map.shape
        
        # 使用阈值生成二值掩码
        threshold = 0.5
        binary_masks = (anomaly_map.squeeze(1) > threshold).long()
        
        # 添加一些随机性以增加多样性
        noise = torch.randint(0, 3, (B, H, W), device=anomaly_map.device)
        random_mask = torch.rand(B, H, W, device=anomaly_map.device) < 0.1
        
        pseudo_masks = torch.where(random_mask, noise, binary_masks)
        
        return pseudo_masks
