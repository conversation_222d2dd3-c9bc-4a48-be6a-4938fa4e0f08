#!/usr/bin/env python3
"""
测试VISA数据集中的特定问题文件
"""

import os
import sys
from PIL import Image

def test_specific_file():
    """测试特定的问题文件"""
    print("=== 测试VISA数据集特定文件 ===")
    
    # 问题文件路径
    problem_file = "F:\\wxx\\AnomalyDetection-SoftPatch-main - 副本 (2)\\visa\\cashew\\train\\good\\240.JPG"
    
    print(f"测试文件: {problem_file}")
    
    # 检查文件是否存在
    if not os.path.exists(problem_file):
        print("❌ 文件不存在")
        
        # 检查可能的路径
        possible_paths = [
            "./visa/cashew/train/good/240.JPG",
            "./visa/cashew/train/good/240.jpg",
            "./VisA/cashew/train/good/240.JPG",
            "./VisA/cashew/train/good/240.jpg"
        ]
        
        print("检查可能的路径:")
        for path in possible_paths:
            if os.path.exists(path):
                print(f"✅ 找到文件: {path}")
                problem_file = path
                break
            else:
                print(f"❌ 不存在: {path}")
        
        if not os.path.exists(problem_file):
            print("❌ 未找到问题文件")
            return False
    
    print(f"✅ 文件存在: {problem_file}")
    
    # 检查文件大小
    file_size = os.path.getsize(problem_file)
    print(f"文件大小: {file_size} bytes")
    
    # 尝试打开文件
    try:
        with Image.open(problem_file) as img:
            print(f"✅ 图像打开成功")
            print(f"   尺寸: {img.size}")
            print(f"   模式: {img.mode}")
            print(f"   格式: {img.format}")
            
            # 尝试加载图像数据
            img.load()
            print("✅ 图像数据加载成功")
            
            # 尝试转换为RGB
            if img.mode != 'RGB':
                rgb_img = img.convert('RGB')
                print(f"✅ 转换为RGB成功: {rgb_img.mode}")
            
            return True
            
    except Exception as e:
        print(f"❌ 图像处理失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 尝试用不同方式打开
        try:
            # 尝试以二进制方式读取文件头
            with open(problem_file, 'rb') as f:
                header = f.read(20)
                print(f"文件头 (hex): {header.hex()}")
                print(f"文件头 (ascii): {header}")
        except Exception as e2:
            print(f"❌ 无法读取文件: {e2}")
        
        return False

def test_visa_dataset_structure():
    """测试VISA数据集结构"""
    print("\n=== 测试VISA数据集结构 ===")
    
    visa_paths = ["./visa", "./VisA"]
    
    for visa_path in visa_paths:
        if os.path.exists(visa_path):
            print(f"✅ 找到VISA数据集: {visa_path}")
            
            # 检查cashew子数据集
            cashew_path = os.path.join(visa_path, "cashew")
            if os.path.exists(cashew_path):
                print(f"✅ 找到cashew子数据集: {cashew_path}")
                
                # 检查train/good目录
                good_path = os.path.join(cashew_path, "train", "good")
                if os.path.exists(good_path):
                    print(f"✅ 找到train/good目录: {good_path}")
                    
                    # 列出该目录下的文件
                    files = os.listdir(good_path)
                    print(f"   文件数量: {len(files)}")
                    
                    # 查找240相关的文件
                    matching_files = [f for f in files if '240' in f]
                    if matching_files:
                        print(f"   找到240相关文件: {matching_files}")
                        
                        for file in matching_files:
                            file_path = os.path.join(good_path, file)
                            print(f"   测试文件: {file_path}")
                            
                            try:
                                with Image.open(file_path) as img:
                                    print(f"     ✅ 正常: {img.size}, {img.mode}")
                            except Exception as e:
                                print(f"     ❌ 损坏: {e}")
                    else:
                        print("   ❌ 未找到240相关文件")
                        # 显示前几个文件
                        print(f"   前5个文件: {files[:5]}")
                else:
                    print(f"❌ 未找到train/good目录: {good_path}")
            else:
                print(f"❌ 未找到cashew子数据集: {cashew_path}")
        else:
            print(f"❌ 未找到VISA数据集: {visa_path}")

def test_visa_dataset_loading():
    """测试VISA数据集加载"""
    print("\n=== 测试VISA数据集加载 ===")
    
    try:
        sys.path.append('src')
        from src.datasets.visa import VISADataset  # 注意是VISADataset不是VisADataset
        
        # 尝试创建数据集
        for visa_path in ["./visa", "./VisA"]:
            if os.path.exists(visa_path):
                print(f"尝试加载数据集: {visa_path}")
                
                try:
                    dataset = VISADataset(
                        source=visa_path,
                        classname="cashew",
                        resize=256,
                        imagesize=224,
                        split="train",
                        train_val_split=1.0
                    )
                    
                    print(f"✅ 数据集创建成功")
                    print(f"   数据集大小: {len(dataset)}")
                    
                    # 尝试加载前几个样本
                    for i in range(min(3, len(dataset))):
                        try:
                            sample = dataset[i]
                            print(f"   样本 {i}: {sample['image'].shape}")
                        except Exception as e:
                            print(f"   ❌ 样本 {i} 加载失败: {e}")
                            # 如果是240.JPG的问题，这里会显示
                            if '240.JPG' in str(e):
                                print(f"     确认是240.JPG文件的问题")
                    
                    return True
                    
                except Exception as e:
                    print(f"❌ 数据集创建失败: {e}")
                    if '240.JPG' in str(e):
                        print("     确认是240.JPG文件导致的问题")
                    return False
        
        print("❌ 未找到VISA数据集路径")
        return False
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 VISA数据集问题诊断")
    
    # 测试特定文件
    file_ok = test_specific_file()
    
    # 测试数据集结构
    test_visa_dataset_structure()
    
    # 测试数据集加载
    dataset_ok = test_visa_dataset_loading()
    
    print(f"\n📊 诊断结果:")
    print(f"   特定文件测试: {'✅' if file_ok else '❌'}")
    print(f"   数据集加载测试: {'✅' if dataset_ok else '❌'}")
    
    if not file_ok:
        print(f"\n💡 解决方案:")
        print(f"1. 删除损坏的240.JPG文件")
        print(f"2. 或者重新下载VISA数据集")
        print(f"3. 或者跳过这个文件继续训练")

if __name__ == "__main__":
    main()
